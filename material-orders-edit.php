<?php
    ini_set('display_errors', 0); // Disable error display
    error_reporting(0); // Suppress error reporting
    include 'config.php';
    include 'auth.php';
    $page_title = 'แก้ไขใบสั่ง Material';
    ob_start();

    // Fetch material order data
    $material_order_id = $_GET['id'];
    $query = "
        SELECT 
            mo.*, 
            c.short_name, 
            c.fullname AS customer_name, 
            c.contact_name, 
            c.credit_day, 
            c.payment_type, 
            c.vat_type 
        FROM 
            material_orders mo 
        INNER JOIN 
            customers c 
        ON 
            mo.customer_id = c.id 
        WHERE 
            mo.id = $material_order_id
    ";
    $result = mysqli_query($conn, $query);
    $material_order = mysqli_fetch_assoc($result);

    // Fetch reservation data by customer_id
    $customer_id = $material_order['customer_id'];
    $query_reservation = "SELECT * FROM reservations WHERE customer_id = $customer_id";
    $result_reservation = mysqli_query($conn, $query_reservation);

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // Process form submission
        $document_date = convert_date_to_db($_POST['document_date']);
        $desired_date = convert_date_to_db($_POST['desired_date']);
        $delivery_date = convert_date_to_db($_POST['delivery_date']);
        $note = $_POST['note'];
        $updated_at = date('Y-m-d H:i:s');
        $user_id = $_SESSION['admin_id'];
        $customer_id = $_POST['customer_id'];
        $reservation_id = $_POST['reservation_id'];

        // Handle image upload
        $profile_image = $material_order['profile_image']; // Keep existing image by default
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
            $target_dir = "upload_image/product/";
            $file_extension = pathinfo($_FILES["profile_image"]["name"], PATHINFO_EXTENSION);
            $unique_file_name = date('YmdHis') . rand(1000, 9999) . '.' . $file_extension;
            $target_file = $target_dir . $unique_file_name;
            if (move_uploaded_file($_FILES["profile_image"]["tmp_name"], $target_file)) {
                $profile_image = $unique_file_name;
            }
        }

        // Update material order
        $query = "UPDATE material_orders SET 
            document_date = '$document_date',
            desired_date = '$desired_date',
            delivery_date = '$delivery_date',
            note = '$note',
            updated_at = '$updated_at',
            profile_image = '$profile_image',
            user_id = '$user_id',
            customer_id = '$customer_id',
            reservation_id = '$reservation_id'
            WHERE id = $material_order_id";
        mysqli_query($conn, $query);

        // Delete existing details
        mysqli_query($conn, "DELETE FROM material_order_details WHERE material_order_id = $material_order_id");

        // Insert updated details
        $detail_no = 1;
        foreach ($_POST['details'] as $detail) {
            $product_id = $detail['product_id'];
            $product_code = $detail['product_code'];
            $product_name = $detail['product_name'];
            $quantity = $detail['quantity'];
            $unit_name = $detail['unit_name'];
            $price_order = $detail['price_order'];
            $size = $detail['size'];
            $country = $detail['country'];
            $material_id = $detail['material_id'];

            $detail_query = "INSERT INTO material_order_details
            (
                id,
                material_order_id,
                product_id,
                product_code,
                product_name,
                quantity,
                unit_name,
                price_order,
                size,
                country,
                material_id
            )
            VALUES
            (
                '$detail_no',
                '$material_order_id',
                '$product_id',
                '$product_code',
                '$product_name',
                '$quantity',
                '$unit_name',
                '$price_order',
                '$size',
                '$country',
                '$material_id'
            )";
            mysqli_query($conn, $detail_query);
            $detail_no++;
        }

        $_SESSION['alert_text'] = 'เพิ่มสินค้าเรียบร้อยแล้ว';
        $_SESSION['alert_icon'] = 'success';

        header('Location: ' . $base_url . '/material-orders-list.php');
        exit();
    }
?>
<div id="appvue">
    <form method="POST" enctype="multipart/form-data">
        <div class="card">
            <div class="card-header" style="background-color:rgb(191, 239, 248);">
                <h3 class="card-title">เพิ่มใบสั่ง Material</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="customer_id">รหัสลูกค้า</label>
                            <select name="customer_id" id="customer_id" class="form-select select2 " required>
                                <option value="0">เลือกข้อมูล</option>
                                <?php
                                    $result = mysqli_query($conn, "SELECT id, short_name FROM customers");
                                    while ($row = mysqli_fetch_assoc($result)):
                                ?>
                                    <option value="<?php echo $row['id']; ?>" <?php echo ($row['id'] == $material_order['customer_id']) ? 'selected' : ''; ?>>
                                        <?php echo $row['short_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="reservation_id">เลขที่ใบรับจอง PO</label>
                            <select name="reservation_id" id="reservation_id" class="form-select">
                                <option value="">-- เลือกใบรับจอง PO --</option>
                                <?php while ($reservation = mysqli_fetch_assoc($result_reservation)): ?>
                                    <option value="<?php echo $reservation['id']; ?>" <?php echo ($reservation['id'] == $material_order['reservation_id']) ? 'selected' : ''; ?>>
                                        <?php echo $reservation['document_number']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="document_number">เลขที่เอกสาร</label>
                            <input type="text" name="document_number" id="document_number" class="form-control" value="<?php echo $material_order['document_number']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="document_date">วันที่</label>
                            <input type="text" value="<?php echo convert_date_to_display($material_order['document_date']); ?>" name="document_date" id="document_date" class="form-control datepicker" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="short_name">ชื่อย่อบริษัท</label>
                            <input type="text" name="short_name" id="short_name" class="form-control bg-light" value="<?php echo $material_order['short_name']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="customer_name">ชื่อบริษัท</label>
                            <input type="text" name="customer_name" id="customer_name" class="form-control bg-light" value="<?php echo $material_order['customer_name']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="desired_date">วันที่ต้องการสินค้า</label>
                            <input type="text" value="<?php echo convert_date_to_display($material_order['desired_date']); ?>" name="desired_date" id="desired_date" class="form-control datepicker" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="admin_name">พนักงานขาย</label>
                            <input type="text" name="admin_name" id="admin_name" value="<?php echo $_SESSION['admin_name']; ?>" class="form-control bg-light" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="department">แผนก</label>
                            <input type="text" name="department" id="department" value="<?php echo $_SESSION['departments_name']; ?>" class="form-control bg-light" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                    <div class="form-group">
                        <label for="profile_image">รูปภาพ</label>
                        <input type="file" name="profile_image" id="profile_image" class="form-control">
                    </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="delivery_date">วันที่กำหนดส่งของ</label>
                            <input type="text" value="<?php echo convert_date_to_display($material_order['delivery_date']); ?>" name="delivery_date" id="delivery_date" class="form-control datepicker" required>
                        </div>
                    </div>
                    <div class="col-md-12">
                    <div class="form-group">
                        <label for="note">หมายเหตุ</label>
                        <textarea name="note" id="note" class="form-control" rows="3" ></textarea>
                    </div>
                </div>


                <!-- table item list -->
                <div class="row mt-5">
                    <div class="col-md-12 mb-3">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#productModal">+ เลือกสินค้า

                        </button>
                    </div>
                    <div class="col md-12">
                        <div class="table-responsive">
                            <table class="table table-bordered border-primary">
                                <thead class="table-info">
                                    <tr>
                                        <th class="text-center">ลำดับ</th>
                                        <th>รหัสสินค้า</th>
                                        <th>ชื่อสินค้า</th>
                                        <th class="text-center">ขนาด</th>
                                        <th class="text-center">จำนวน</th>
                                        <th class="text-center">หน่วยนับ</th>
                                        <th class="text-end">ประเทศ</th>
                                        <th class="text-end">วัตถุดิบ</th>
                                        <th class="text-end">ราคา</th>
                                        <th class="text-center">PDF</th>
                                        <th>&nbsp;</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template v-if="reservationItems.length > 0">
                                        <tr v-for="(item, index) in reservationItems" :key="index">
                                            <td class="text-center">{{ index+1 }}</td>
                                            <td>
                                                {{ item.product_code }}
                                                <input type="hidden" :name="'details['+ index +'][product_id]'" :value="item.product_id">
                                                <input type="hidden" :name="'details['+ index +'][product_code]'" :value="item.product_code">
                                            </td>
                                            <td>
                                                {{ item.product_name }}
                                                <input type="hidden" :name="'details['+ index +'][product_name]'" :value="item.product_name">
                                            </td>
                                            <td class="text-center">
                                                {{ item.size }}
                                                <input type="text" v-model="item.size" :name="'details['+ index +'][size]'" class="form-control form-control-sm text-center" required>
                                            </td>
                                            <td class="text-end">
                                                <input type="number" v-model="item.quantity" :name="'details['+ index +'][quantity]'" class="form-control form-control-sm text-end" required>
                                            </td>
                                            <td class="text-center">
                                                {{ item.unit_name }}
                                                <input type="hidden" :name="'details['+ index +'][unit_name]'" :value="item.unit_name">
                                            </td>
                                            <td class="text-center">
                                                {{ item.country }}
                                                <select v-model="item.country" :name="'details['+ index +'][country]'" class="form-select form-select-sm">
                                                    <option value="">เลือกประเทศ</option>
                                                    <option value="ไทย">ไทย</option>
                                                    <option value="จีน">จีน</option>
                                                    <option value="ญี่ปุ่น">ญี่ปุ่น</option>
                                                    <option value="เกาหลี">เกาหลี</option>
                                                </select>
                                            </td>
                                            <td class="text-center">
                                                {{ item.material_name }}
                                                <select v-model="item.material_id" :name="'details['+ index +'][material_id]'" class="form-select form-select-sm">
                                                    <option value="">เลือกวัตถุดิบ</option>
                                                    <?php
                                                        $result = mysqli_query($conn, "SELECT id, materials_name FROM materials");
                                                        while ($row = mysqli_fetch_assoc($result)):
                                                    ?>
                                                        <option value="<?php echo $row['id']; ?>"><?php echo $row['materials_name']; ?></option>
                                                    <?php endwhile; ?>
                                                </select>
                                            </td>
                                            <td class="text-center">
                                                {{ item.price_order }}
                                                <input type="text" v-model="item.price_order" :name="'details['+ index +'][price_order]'" class="form-control form-control-sm text-center">
                                            </td>
                                            <td class="text-center">
                                                <a v-if="item.pdf && item.pdf.length > 0" :href="item.pdf" target="_blank" class="btn btn-info"><i class="bi bi-file-pdf"></i></a>
                                            </td>
                                            <td class="text-center">
                                                <button type="button" @click="removeReservationItem(index)" class="btn btn-danger"><i class="bi bi-trash"></i></button>
                                            </td>
                                        </tr>
                                    </template>
                                    <template v-else>
                                        <tr>
                                            <td colspan="10" class="text-center">ไม่มีข้อมูล</td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button type="submit" class="btn btn-primary me-1"><i class="bi bi-floppy me-1"></i>บันทึกข้อมูล</button>
                <a href="<?php echo $base_url . '/material-orders-list.php'; ?>" class='btn btn-secondary'>ย้อนกลับ</a>
            </div>
        </div>
    </form>    <div class="modal fade" id="productModal" tabindex="-1" aria-labelledby="productModalLabel">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="productModalLabel">เลือกสินค้า</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Search Input -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" 
                                       v-model="searchQuery" 
                                       @keyup.enter="searchProducts($event)"
                                       class="form-control" 
                                       placeholder="ค้นหาสินค้า (รหัสสินค้า หรือ ชื่อสินค้า)">
                            </div>
                            <div class="col-md-2">
                                <button type="button" @click="searchProducts($event)" class="btn btn-outline-secondary">
                                    <i class="bi bi-search"></i> ค้นหา
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered border-primary">
                            <thead class="table-info">
                                <tr>
                                    <th class="text-center">
                                        <input type="checkbox" v-model="selectAll" @change="toggleSelectAll">
                                    </th>
                                    <th>รหัสสินค้า</th>
                                    <th>ชื่อสินค้า</th>
                                    <th class="text-center">จำนวน</th>
                                    <th class="text-center">หน่วยนับ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(product, index) in products" :key="index">
                                    <td class="text-center">
                                        <input type="checkbox" v-model="product.selected" @change="updateSelectAll">
                                    </td>
                                    <td>{{ product.product_code }}</td>
                                    <td>{{ product.product_name }}</td>
                                    <td class="text-center">{{ product.quantity }}</td>
                                    <td class="text-center">{{ product.unit_name }}</td>
                                </tr>
                                <tr v-if="products.length === 0">
                                    <td colspan="5" class="text-center">ไม่พบข้อมูลสินค้า</td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <!-- Pagination Controls -->
                        <div class="d-flex justify-content-between align-items-center mt-3" v-if="totalPages > 1">
                            <div>
                                <span class="text-muted">
                                    แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} - {{ Math.min(currentPage * itemsPerPage, totalProducts) }} 
                                    จาก {{ totalProducts }} รายการ
                                </span>
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0">
                                    <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                        <button type="button" class="page-link" @click="prevPage($event)" :disabled="currentPage === 1">
                                            <i class="bi bi-chevron-left"></i>
                                        </button>
                                    </li>
                                    
                                    <li v-for="page in Math.min(totalPages, 5)" :key="page" 
                                        class="page-item" 
                                        :class="{ active: currentPage === page }">
                                        <button type="button" class="page-link" @click="goToPage(page, $event)">
                                            {{ page }}
                                        </button>
                                    </li>
                                    
                                    <li v-if="totalPages > 5 && currentPage < totalPages - 2" class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    
                                    <li v-if="totalPages > 5 && currentPage < totalPages - 1" 
                                        class="page-item" 
                                        :class="{ active: currentPage === totalPages }">
                                        <button type="button" class="page-link" @click="goToPage(totalPages, $event)">
                                            {{ totalPages }}
                                        </button>
                                    </li>
                                    
                                    <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                        <button type="button" class="page-link" @click="nextPage($event)" :disabled="currentPage === totalPages">
                                            <i class="bi bi-chevron-right"></i>
                                        </button>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
                    <button type="button" @click="onSelectProduct()" class="btn btn-primary">เลือกข้อมูล</button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
    $content = ob_get_clean();
    $js_script = '<script src="' . $base_url . '/assets/js/material-order.js"></script>';
    include 'template_master.php';
?>
