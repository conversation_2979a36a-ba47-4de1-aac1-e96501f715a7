<?php
include 'config.php';
include 'auth.php';

// Get the reservation ID from the query string
$id = $_GET['id'];

// Check if the reservation is referenced in the invoices table
$invoiceCheckQuery = "SELECT COUNT(*) as count FROM invoices WHERE reservation_id = $id";
$invoiceCheckResult = mysqli_query($conn, $invoiceCheckQuery);
$invoiceCheckRow = mysqli_fetch_assoc($invoiceCheckResult);

if ($invoiceCheckRow['count'] > 0) {
    // Set error message if the reservation is referenced
    $_SESSION['alert_text'] = 'ไม่สามารถลบข้อมูลได้ เนื่องจากมีการอ้างอิงในใบแจ้งหนี้';
    $_SESSION['alert_icon'] = 'error';
} else {
    // Restore product quantities before deleting reservation
    $restore_query = "
        UPDATE products p 
        INNER JOIN reservation_details rd ON p.product_code = rd.product_code 
        SET p.quantity = p.quantity - rd.quantity, p.reservation_id = 0 
        WHERE rd.reservation_id = $id
    ";
    mysqli_query($conn, $restore_query);
    
    // Delete reservation details
    $reservationsDetailsQuery = "DELETE FROM reservation_details WHERE reservation_id = $id";
    mysqli_query($conn, $reservationsDetailsQuery);

    // Delete the reservation
    $reservationQuery = "DELETE FROM reservations WHERE id = $id";
    mysqli_query($conn, $reservationQuery);
    
    // Update quotations to remove reservation reference
    $update_quotation_query = "UPDATE quotations SET reservation_id = NULL WHERE reservation_id = $id";
    mysqli_query($conn, $update_quotation_query);

    // Set success message
    $_SESSION['alert_text'] = 'ลบข้อมูลเรียบร้อยแล้ว';
    $_SESSION['alert_icon'] = 'success';
}

// Redirect to the reservations list
header('Location: ' . $base_url . '/reservations-list.php');
exit;
?>
