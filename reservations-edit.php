<?php
    ini_set('display_errors', 0); // Disable error display
    error_reporting(0); // Suppress error reporting
    include 'config.php';
    include 'auth.php';
    $page_title = 'แก้ไขใบรับจองสินค้า';
    ob_start();

    // Fetch reservation data
    $reservation_id = $_GET['id'];
    $query = "
        SELECT 
            r.*, 
            c.short_name, 
            c.fullname AS customer_name, 
            c.contact_name, 
            c.credit_day, 
            c.payment_type, 
            c.vat_type 
        FROM 
            reservations r 
        INNER JOIN 
            customers c 
        ON 
            r.customer_id = c.id 
        WHERE 
            r.id = $reservation_id
    ";
    $result = mysqli_query($conn, $query);
    $reservation = mysqli_fetch_assoc($result);

    // Fetch quotation data by customer_id
    $customer_id = $reservation['customer_id'];
    $query_qo = "SELECT * FROM quotations WHERE customer_id = $customer_id";
    $result_qo = mysqli_query($conn, $query_qo);
    

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // Assign fields to variables
        $document_date = convert_date_to_db($_POST['document_date']);
        $purchase_order = $_POST['purchase_order'];
        $delivery_date = convert_date_to_db($_POST['delivery_date']);
        $order_date = convert_date_to_db($_POST['order_date']);
        $credit_day = $_POST['credit_day'];
        $payment_type = $_POST['payment_type'];
        $vat_type = $_POST['vat_type'];
        $sub_total = $_POST['sub_total'];
        $discount = $_POST['discount'];
        $after_discount = $_POST['after_discount'];
        $vat_amount = $_POST['vat_amount'];
        $grand_total = $_POST['grand_total'];
        $note = $_POST['note'];
        $updated_at = date('Y-m-d H:i:s');
        $user_id = $_SESSION['admin_id'];
        $customer_id = $_POST['customer_id'];

         // Handle image upload
        $profile_image = isset($reservation['profile_image']) ? $reservation['profile_image'] : '';

        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
            $target_dir = "upload_image/product/";
            $file_extension = pathinfo($_FILES["profile_image"]["name"], PATHINFO_EXTENSION);
            $unique_file_name = date('YmdHis') . rand(1000, 9999) . '.' . $file_extension;
            $target_file = $target_dir . $unique_file_name;

            if (move_uploaded_file($_FILES["profile_image"]["tmp_name"], $target_file)) {
            // Remove old image if it exists
            if (!empty($profile_image) && file_exists($target_dir . $profile_image)) {
                unlink($target_dir . $profile_image);
            }
            $profile_image = $unique_file_name;
            }
        }

        $query = "UPDATE reservations SET 
            document_date = '$document_date',
            purchase_order = '$purchase_order',
            delivery_date = '$delivery_date',
            order_date = '$order_date',
            credit_day = '$credit_day',
            payment_type = '$payment_type',
            vat_type = '$vat_type',
            sub_total = '$sub_total',
            discount = '$discount',
            after_discount = '$after_discount',
            vat_amount = '$vat_amount',
            grand_total = '$grand_total',
            note = '$note',
            updated_at = '$updated_at',
            user_id = '$user_id',
            profile_image = '$profile_image',
            customer_id = '$customer_id'
        WHERE id = $reservation_id";        mysqli_query($conn, $query);

        // Restore product quantities before deleting existing details
        $restore_query = "
            UPDATE products p 
            INNER JOIN reservation_details rd ON p.product_code = rd.product_code 
            SET p.quantity = p.quantity - rd.quantity, p.reservation_id = 0 
            WHERE rd.reservation_id = $reservation_id
        ";
        mysqli_query($conn, $restore_query);

        // Delete existing details
        mysqli_query($conn, "DELETE FROM reservation_details WHERE reservation_id = $reservation_id");

        // Insert updated details
        $detail_no = 1;
        $reservation_id = $reservation_id; // Ensure $reservation_id is initialized
        foreach ($_POST['details'] as $detail) {
            $product_id = $detail['product_id'];
            $product_code = $detail['product_code'];
            $product_name = $detail['product_name'];
            $quantity = $detail['quantity'];
            $unit_name = $detail['unit_name'];
            $price = $detail['price'];
            $discount = $detail['discount'];
            $total = ($price * $quantity) - $discount;

            $detail_query = "INSERT INTO reservation_details 
            (
                id,
                reservation_id,
                product_id,
                product_code, 
                product_name, 
                quantity, 
                unit_name, 
                price, 
                discount, 
                total
            ) 
            VALUES 
            (
                '$detail_no',
                '$reservation_id',
                '$product_id',
                '$product_code', 
                '$product_name', 
                '$quantity', 
                '$unit_name', 
                '$price', 
                '$discount', 
                '$total'
            )";
            mysqli_query($conn, $detail_query);
                $detail_no++;
            
            // Add reserved quantity to product stock and set reservation_id
            $update_product_query = "UPDATE products SET reservation_id = '$reservation_id', quantity = quantity + $quantity WHERE product_code = '$product_code'";
            mysqli_query($conn, $update_product_query);

        }

        $_SESSION['alert_text'] = 'แก้ไขข้อมูลเรียบร้อยแล้ว';
        header('Content-Type: application/json'); // Ensure JSON response
        echo json_encode(['status' => 'success', 'message' => 'แก้ไขข้อมูลเรียบร้อยแล้ว']);

        header('Location: ' . $base_url . '/reservations-list.php');
        exit();
    }
?>
<div id="appvue">    
    <form method="POST" enctype="multipart/form-data">
        <div class="card">
            <div class="card-header" style="background-color:rgb(191, 239, 248);">
                <h3 class="card-title">เพิ่มใบรับจองสินค้า</h3>
                <div class="d-flex justify-content-end">
                    <a href="<?php echo $base_url . '/reservations-view.php?id=' . $reservation_id; ?>" class="btn btn-primary me-1">ตรวจสอบข้อมูล Invoices</a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="customer_id">รหัสลูกค้า</label>
                            <select name="customer_id" id="customer_id" class="form-select select2 " required>
                                <option value="0">เลือกข้อมูล</option>
                                <?php
                                    $result = mysqli_query($conn, "SELECT id, short_name FROM customers");
                                    while ($row = mysqli_fetch_assoc($result)):
                                ?>
                                    <option value="<?php echo $row['id']; ?>" <?php echo ($row['id'] == $reservation['customer_id']) ? 'selected' : ''; ?>>
                                        <?php echo $row['short_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="quotation_id">เลขที่ใบเสนอราคา</label>
                            <select name="quotation_id" id="quotation_id" class="form-select">
                                <option value="">-- เลือกใบเสนอราคา --</option>
                                <?php foreach ($result_qo as $quotation): ?>
                                    <option value="<?php echo $quotation['id']; ?>" <?php echo ($quotation['id'] == $reservation['quotation_id']) ? 'selected' : ''; ?>>
                                        <?php echo $quotation['document_no']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="document_number">เลขที่เอกสาร</label>
                            <input type="text" name="document_number" id="document_number" class="form-control" value="<?php echo $reservation['document_number']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="document_date">วันที่</label>
                            <input type="text" value="<?php echo convert_date_to_display($reservation['document_date']); ?>" name="document_date" id="document_date" class="form-control datepicker" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="short_name">ชื่อย่อบริษัท</label>
                            <input type="text" name="short_name" id="short_name" class="form-control bg-light" value="<?php echo $reservation['short_name']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="customer_name">ชื่อบริษัท</label>
                            <input type="text" name="customer_name" id="customer_name" class="form-control bg-light" value="<?php echo $reservation['customer_name']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="order_date">วันที่สั่งผลิต</label>
                            <input type="text" value="<?php echo convert_date_to_display($reservation['order_date']); ?>" name="order_date" id="order_date" class="form-control datepicker" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="contact_name">ชื่อผู้ติดต่อ</label>
                            <input type="text" name="contact_name" id="contact_name" class="form-control bg-light" value="<?php echo $reservation['contact_name']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="admin_name">พนักงานขาย</label>
                            <input type="text" name="admin_name" id="admin_name" value="<?php echo $_SESSION['admin_name']; ?>" class="form-control bg-light" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="department">แผนก</label>
                            <input type="text" name="department" id="department" value="<?php echo $_SESSION['departments_name']; ?>" class="form-control bg-light" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="delivery_date">วันที่กำหนดส่ง</label>
                            <input type="text" value="<?php echo convert_date_to_display($reservation['delivery_date']); ?>" name="delivery_date" id="delivery_date" class="form-control datepicker" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="purchase_order">เลขที่ที่ใบสั่งซื้อ (PO)</label>
                            <input type="text" name="purchase_order" id="purchase_order" class="form-control" value="<?php echo $reservation['purchase_order']; ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="payment_type">การชำระเงิน</label>
                            <input type="text" name="payment_type" id="payment_type" class="form-control bg-light" value="<?php echo $reservation['payment_type']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="credit_day">เครดิต (วัน)</label>
                            <input type="number" name="credit_day" id="credit_day" class="form-control bg-light" value="<?php echo $reservation['credit_day']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="vat_type_name">ชนิดภาษี</label>
                            <input type="text" name="vat_type_name" id="vat_type_name" value="<?php echo trans_vattype_to_display($reservation['vat_type']); ?>" class="form-control bg-light" readonly>
                            <input type="hidden" name="vat_type" id="vat_type" value="<?php echo $reservation['vat_type']; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="profile_image">PDF</label>
                            <input type="file" name="profile_image" id="profile_image" class="form-control" accept="application/pdf">
                            <?php if (!empty($reservation['profile_image'])): ?>
                                <a href="<?php echo $base_url; ?>/upload_image/product/<?php echo $reservation['profile_image']; ?>" target="_blank">
                                    ดาวน์โหลดไฟล์ PDF
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="note">หมายเหตุ</label>
                        <textarea name="note" id="note" class="form-control" rows="3"><?php echo $reservation['note']; ?></textarea>
                    </div>

                <!-- table item list -->
                <div class="row mt-5">
                    <div class="col-md-12 mb-3">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#productModal">+ เลือกสินค้า
                         
                        </button>
                    </div>
                    <div class="col md-12">
                        <div class="table-responsive">
                            <table class="table table-bordered border-primary">
                                <thead class="table-info">
                                    <tr>
                                        <th class="text-center">ลำดับ</th>
                                        <th>รหัสสินค้า</th>
                                        <th>ชื่อสินค้า</th>
                                        <th class="text-center">จำนวน</th>
                                        <th class="text-center">หน่วยนับ</th>
                                        <th class="text-end">ราคา</th>
                                        <th class="text-end">ส่วนลด</th>
                                        <th class="text-end">มูลค่า</th>
                                        <th class="text-center">PDF</th>
                                        <th>&nbsp;</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template v-if="reservationItems.length > 0">
                                        <tr v-for="(item, index) in reservationItems" :key="index">
                                            <td class="text-center">{{ index+1 }}</td>
                                            <td>
                                                {{ item.product_code }}
                                                <input type="hidden" :name="'details['+ index +'][product_id]'" :value="item.product_id">
                                                <input type="hidden" :name="'details['+ index +'][product_code]'" :value="item.product_code">
                                            </td>
                                            <td>
                                                {{ item.product_name }}
                                                <input type="hidden" :name="'details['+ index +'][product_name]'" :value="item.product_name">
                                            </td>
                                            <td class="text-end">
                                                <input type="number" v-model="item.quantity" :name="'details['+ index +'][quantity]'" class="form-control form-control-sm text-end" required>
                                            </td>
                                            <td class="text-center">
                                                {{ item.unit_name }}
                                                <input type="hidden" :name="'details['+ index +'][unit_name]'" :value="item.unit_name">
                                            </td>
                                            <td class="text-end">
                                                <input type="number" v-model="item.price" :name="'details['+ index +'][price]'" class="form-control form-control-sm text-end" required>
                                            </td>
                                            <td class="text-end">
                                                <input type="number" v-model="item.discount" :name="'details['+ index +'][discount]'" class="form-control form-control-sm text-end" required>
                                            </td>
                                            <td class="text-end">{{ calculateTotal(item).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</td>
                                            <td class="text-center">
                                                <a v-if="item.pdf && item.pdf.length > 0" :href="item.pdf" target="_blank" class="btn btn-info"><i class="bi bi-file-pdf"></i></a>
                                            </td>
                                            <td class="text-center">
                                                <button type="button" @click="removeReservationItem(index)" class="btn btn-danger"><i class="bi bi-trash"></i></button>
                                            </td>
                                        </tr>
                                    </template>
                                    <template v-else>
                                        <tr>
                                            <td colspan="10" class="text-center">ไม่มีข้อมูล</td>
                                        </tr>   
                                    </template>

                                    <tr>
                                        <td colspan="7" class="text-end"><strong>รวมจำนวนเงิน</strong></td>
                                        <td class="text-end">
                                            {{ calculateSubTotal().toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                            <input type="hidden" name="sub_total" :value="subTotal">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>       
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>ส่วนลดทั้งใบ</strong></td>
                                        <td class="text-end">
                                            <input type="text" name="discount" id="discount" @keyup="calculateGrandTotal()" value="<?php echo $reservation['discount']; ?>" class="form-control form-control-sm text-end">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>รวมหลังหักส่วนลด</strong></td>
                                        <td class="text-end">
                                            {{ Number(afterDiscount).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                            <input type="hidden" name="after_discount" :value="afterDiscount">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>ภาษีมูลค่าเพิ่ม 7%</strong></td>
                                        <td class="text-end">
                                            {{ Number(vat).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                            <input type="hidden" name="vat_amount" :value="vat">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>ราคาสุทธิ</strong></td>
                                        <td class="text-end">
                                            {{ Number(calculateGrandTotal()).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                            <input type="hidden" name="grand_total" :value="grandTotal">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button type="submit" class="btn btn-primary me-1"><i class="bi bi-floppy me-1"></i>บันทึกข้อมูล</button>
                <a href="<?php echo $base_url . '/reservations-list.php'; ?>" class='btn btn-secondary'>ย้อนกลับ</a>
            </div>
        </div>
    </form>
    <div class="modal fade" id="productModal" tabindex="-1" aria-labelledby="productModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="productModalLabel">เลือกสินค้า</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>                <div class="modal-body">
                    <!-- Search Input -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">                                <input type="text" 
                                       v-model="searchQuery" 
                                       @keyup.enter="searchProducts($event)"
                                       class="form-control" 
                                       placeholder="ค้นหาสินค้า (รหัสสินค้า หรือ ชื่อสินค้า)">
                            </div>
                            <div class="col-md-2">
                                <button type="button" @click="searchProducts" class="btn btn-outline-secondary">
                                    <i class="bi bi-search"></i> ค้นหา
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered border-primary">
                            <thead class="table-info">
                            <tr>             
                                <th><input type="checkbox" v-model="selectAll" @change="toggleSelectAll"></th>       
                                <th>รหัสสินค้า</th>
                                <th>ชื่อสินค้า</th>
                                <th>จำนวน</th>
                                <th>หน่วยนับ</th>
                                <th>ราคา</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(product, index) in products" :key="index">
                                <td><input type="checkbox" v-model="product.selected" @change="updateSelectAll"></td>
                                <td>{{ product.product_code }}</td>
                                <td>{{ product.product_name }}</td>
                                <td>{{ product.quantity }}</td>
                                <td>{{ product.unit_name }}</td>
                                <td>{{ product.price }}</td>
                            </tr>
                            <tr v-if="products.length === 0">
                                <td colspan="6" class="text-center">ไม่พบข้อมูลสินค้า</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- Pagination Controls -->
                    <div class="d-flex justify-content-between align-items-center mt-3" v-if="totalPages > 1">
                        <div>
                            <span class="text-muted">
                                แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} - {{ Math.min(currentPage * itemsPerPage, totalProducts) }} 
                                จาก {{ totalProducts }} รายการ
                            </span>
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                    <button class="page-link" @click="prevPage" :disabled="currentPage === 1">
                                        <i class="bi bi-chevron-left"></i>
                                    </button>
                                </li>
                                
                                <li v-for="page in Math.min(totalPages, 5)" :key="page" 
                                    class="page-item" 
                                    :class="{ active: currentPage === page }">
                                    <button class="page-link" @click="goToPage(page)">
                                        {{ page }}
                                    </button>
                                </li>
                                
                                <li v-if="totalPages > 5 && currentPage < totalPages - 2" class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                
                                <li v-if="totalPages > 5 && currentPage < totalPages - 1" 
                                    class="page-item"
                                    :class="{ active: currentPage === totalPages }">
                                    <button class="page-link" @click="goToPage(totalPages)">
                                        {{ totalPages }}
                                    </button>
                                </li>
                                
                                <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                    <button class="page-link" @click="nextPage" :disabled="currentPage === totalPages">
                                        <i class="bi bi-chevron-right"></i>
                                    </button>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
                    <button type="button" @click="onSelectProduct()" class="btn btn-primary">เลือกข้อมูล</button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
    $content = ob_get_clean();
    $css_style = '<style>
        .pagination .page-link {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .pagination .page-item.active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
            border-color: #dee2e6;
        }
    </style>';
    $js_script = '<script src="' . $base_url . '/assets/js/reservation.js"></script>';
    include 'template_master.php';
?>
