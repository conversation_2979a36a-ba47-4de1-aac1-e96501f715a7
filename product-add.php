<?php
    include 'config.php';
    include 'auth.php';
    $page_title = 'เพิ่มสินค้า';
    ob_start();


    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $product_name = $_POST['product_name'];
        $product_code = $_POST['product_code'];
        $unit_name = $_POST['unit_name'];
        $quantity = isset($_POST['quantity']) ? $_POST['quantity'] : 0;
        $price = isset($_POST['price']) ? $_POST['price'] : 0;
        $detail = isset($_POST['detail']) ? $_POST['detail'] : '';
        $commission = isset($_POST['commission']) ? $_POST['commission'] : 0;
        $created_at = date('Y-m-d H:i:s');
        $customer_id = $_POST['customer_id'];
        $material_id = $_POST['material_id'];
        $hardnes_id = $_POST['hardnes_id'];
        $category_id = isset($_POST['category_id']) ? $_POST['category_id'] : 0;

        // Handle image upload
        $profile_image = '';
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
            $target_dir = "upload_image/product/";
            $file_extension = pathinfo($_FILES["profile_image"]["name"], PATHINFO_EXTENSION);
            $unique_file_name = date('YmdHis') . rand(1000, 9999) . '.' . $file_extension;
            $target_file = $target_dir . $unique_file_name;
            if (move_uploaded_file($_FILES["profile_image"]["tmp_name"], $target_file)) {
                $profile_image = $unique_file_name;
            }
        }
        // Check product code length
        if (strlen($product_code) < 11) {
            $_SESSION['alert_text'] = 'รหัสสินค้าต้องมีความยาวอย่างน้อย 11 ตัวอักษร';
            $_SESSION['alert_icon'] = 'error';
            header('Location: ' . $base_url . '/product-add.php');
            exit();
        }

        // Check for duplicate product code AND product name
        $query = "SELECT 1 FROM products WHERE product_code = ? OR product_name = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'ss', $product_code, $product_name);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_store_result($stmt);

        if (mysqli_stmt_num_rows($stmt) > 0) {
            $_SESSION['alert_text'] = 'รหัสสินค้าหรือชื่อสินค้านี้มีอยู่ในระบบแล้ว';
            $_SESSION['alert_icon'] = 'error';
            header('Location: ' . $base_url . '/product-add.php');
            exit();
        }

        mysqli_stmt_close($stmt);

        $query = "INSERT INTO products 
        (
            product_code, 
            product_name, 
            quantity, 
            price, 
            unit_name,
            profile_image, 
            detail, 
            created_at, 
            customer_id,
            category_id,
            material_id,
            commission,
            hardnes_id
        )
         VALUES (
            '$product_code',
            '$product_name', 
            '$quantity', 
            '$price', 
            '$unit_name', 
            '$profile_image', 
            '$detail', 
            '$created_at', 
            '$customer_id',
            '$category_id',
            '$material_id',
            '$commission',
            '$hardnes_id'
         )";
        mysqli_query($conn, $query);
        $_SESSION['alert_text'] = 'เพิ่มสินค้าเรียบร้อยแล้ว';
        $_SESSION['alert_icon'] = 'success';
        
        header('Location: ' . $base_url . '/product-list.php');
        exit();
    }
?>
<form method="POST" enctype="multipart/form-data">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">เพิ่มสินค้า</h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="customer_id">รหัสลูกค้า</label>
                        <select name="customer_id" id="customer_id" class="form-select select2" required>
                            <option value="0">--เลือกข้อมูล--</option>
                            <?php
                                $result = mysqli_query($conn, "SELECT id, customer_code, short_name FROM customers");
                                while ($row = mysqli_fetch_assoc($result)):
                            ?>
                                <option value="<?php echo $row['id']; ?>">
                                    <?php echo $row['customer_code'] . ' - ' . $row['short_name']; ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="product_code">รหัสสินค้า</label>
                        <input type="text" name="product_code" id="product_code" class="form-control" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="product_name">ชื่อสินค้า</label>
                        <input type="text" name="product_name" id="product_name" class="form-control" required>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="price">ราคา</label>
                        <input type="text" name="price" id="price" class="form-control" required oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');" onblur="this.value = parseFloat(this.value).toFixed(2);">
                    </div>
                </div>
                <div class="col-md-1">
                    <div class="form-group">
                        <label for="unit_name">หน่วยนับ</label>
                        <select name="unit_name" id="unit_name" class="form-select" required>
                            <option value="PCS">PCS</option>
                            <option value="SET">SET</option>
                            <option value="NR">NR</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="category_id">กลุ่มประเภทสินค้า</label>
                        <select name="category_id" id="category_id" class="form-select select2" required>
                            <option value="0">--เลือกข้อมูล--</option>
                            <?php
                                $result = mysqli_query($conn, "SELECT id, category_code, category_name FROM categories");
                                while ($row = mysqli_fetch_assoc($result)):
                            ?>
                                <option value="<?php echo $row['id']; ?>"><?php echo $row['category_code'] . ' - ' . $row['category_name']; ?></option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="material_id">วัตถุดิบ</label>
                        <select name="material_id" id="material_id" class="form-select select2" required>
                            <option value="0">--เลือกข้อมูล--</option>
                            <?php
                                $result = mysqli_query($conn, "SELECT id,  materials_code, materials_name FROM materials");
                                while ($row = mysqli_fetch_assoc($result)):
                            ?>
                                <option value="<?php echo $row['id']; ?>"><?php echo $row['materials_code'] . ' - ' . $row['materials_name']; ?></option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="hardnes_id">ค่าความแข็ง</label>
                        <select name="hardnes_id" id="hardnes_id" class="form-select select2" required>
                            <!-- <option value="0">--เลือกข้อมูล--</option> -->
                            <?php
                                $result = mysqli_query($conn, "SELECT id,  hardness_name FROM hardness");
                                while ($row = mysqli_fetch_assoc($result)):
                            ?>
                                <option value="<?php echo $row['id']; ?>"><?php echo $row['hardness_name']; ?></option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="commission">ค่าคอม</label>
                        <input type="text" name="commission" id="commission" class="form-control">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="profile_image">รูปภาพ</label>
                        <input type="file" name="profile_image" id="profile_image" class="form-control">
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <button type="submit" class="btn btn-primary"><i class="bi bi-floppy me-1"></i>บันทึกข้อมูล</button>
            <a href="<?php echo $base_url . '/product-list.php'; ?>" class='btn btn-secondary'>ย้อนกลับ</a>
        </div>
    </div>
</form>
<?php
    $content = ob_get_clean();
    $js_script = '<script src="' . $base_url . '/assets/js/product.js"></script>';
    include 'template_master.php';
?>
