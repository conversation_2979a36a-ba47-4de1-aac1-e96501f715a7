<?php
    include 'config.php';
    include 'auth.php';
    $page_title = 'ใบสั่งซื้อ';
    ob_start();

    // Handle search and filter
    $search_keyword = isset($_GET['search_keyword']) ? $_GET['search_keyword'] : '';
    $category_id = isset($_GET['category_id']) ? $_GET['category_id'] : '';

    // Handle sorting
    $sort_by = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'id';
    $sort_order = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'desc';
    $next_sort_order = $sort_order == 'asc' ? 'desc' : 'asc';

    // Pagination settings
    $limit = 10;
    $page = isset($_GET['page']) ? $_GET['page'] : 1;
    $offset = ($page - 1) * $limit;

    // Build query
    $query = "SELECT 
    rq.*,
    r.document_no AS requisition_no,
    s.short_name,
    s.fullname
    FROM orders rq 
    LEFT JOIN requisitions r
    ON rq.requisition_id = r.id
    INNER JOIN suppliers s 
    ON rq.supplier_id = s.id 
    WHERE 1=1";
    
    if ($search_keyword) {
        $query .= " AND (rq.order_no LIKE '%$search_keyword%' OR s.short_name LIKE '%$search_keyword%' OR s.fullname LIKE '%$search_keyword%')";
    }
    
    $query .= " ORDER BY $sort_by $sort_order";
    $query .= " LIMIT $limit OFFSET $offset";
    
    // Check if the 'supplier_id' column exists in the 'orders' table
    $check_column_query = "SHOW COLUMNS FROM orders LIKE 'supplier_id'";
    $check_column_result = mysqli_query($conn, $check_column_query);
    
    if (mysqli_num_rows($check_column_result) == 0) {
        // If 'supplier_id' column doesn't exist, modify the query
        $query = "SELECT 
        rq.*,
        s.short_name,
        s.fullname
        FROM orders rq 
        LEFT JOIN suppliers s 
        ON rq.id = s.id 
        WHERE 1=1";
        
        if ($search_keyword) {
            $query .= " AND (rq.order_no LIKE '%$search_keyword%' OR s.short_name LIKE '%$search_keyword%' OR s.fullname LIKE '%$search_keyword%')";
        }
        
        $query .= " ORDER BY $sort_by $sort_order";
        $query .= " LIMIT $limit OFFSET $offset";
    }
    
    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        die("Query failed: " . mysqli_error($conn));
    }
    
    // Get total records for pagination
    $total_query = "SELECT COUNT(*) as total FROM orders rq 
    LEFT JOIN suppliers s ON rq.id = s.id 
    WHERE 1=1";
    
    if ($search_keyword) {
        $total_query .= " AND (rq.order_no LIKE '%$search_keyword%' OR s.short_name LIKE '%$search_keyword%' OR s.fullname LIKE '%$search_keyword%')";
    }
    
    $total_result = mysqli_query($conn, $total_query);
    
    if (!$total_result) {
        die("Total query failed: " . mysqli_error($conn));
    }
    
    $total_row = mysqli_fetch_assoc($total_result);
    
    $total_records = $total_row['total'];
    
    $total_pages = ceil($total_records / $limit);
    

    // Determine sort icon
    function get_sort_icon($current_sort_by, $current_sort_order, $column) {
        if ($current_sort_by == $column) {
            return $current_sort_order == 'asc' ? '<i class="bi bi-sort-alpha-down"></i>' : '<i class="bi bi-sort-alpha-down-alt"></i>';
        }
        return '';
    }
?>
<div class="card">
    <div class="card-header">
        <h3 class="card-title">ใบสั่งซื้อ</h3>
        <div class="card-tools">
            <a href="<?php echo $base_url; ?>/orders-add.php" class="btn btn-primary">เพิ่มใบสั่งซื้อ</a>
        </div>
    </div>
    <div class="card-body">
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger">
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            </div>
        <?php endif; ?>
        <form method="GET" class="mb-3">
            <div class="row">
                <div class="col-md-4">
                    <input type="text" name="search_keyword" class="form-control" placeholder="ค้นหาใบสั่งซื้อ" value="<?php echo $search_keyword; ?>">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary">ค้นหา</button>
                </div>
            </div>
        </form>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>
                            <a href="?sort_by=order_no&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                                เลขที่เอกสาร <?php echo get_sort_icon($sort_by, $sort_order, 'order_no'); ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort_by=requisition_no&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                                เลขที่ใบขอซื้อ <?php echo get_sort_icon($sort_by, $sort_order, 'requisition_no'); ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort_by=document_revision&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                                แก้ไข Rev. <?php echo get_sort_icon($sort_by, $sort_order, 'document_revision'); ?>
                            </a>
                        </th>
                        <th>วันที่</th>
                        <th>
                            <a href="?sort_by=short_name&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                                ชื่อย่อซัพพลายเออร์ <?php echo get_sort_icon($sort_by, $sort_order, 'short_name'); ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort_by=fullname&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                                ชื่อซัพพลายเออร์ <?php echo get_sort_icon($sort_by, $sort_order, 'fullname'); ?>
                            </a>
                        </th>
                        <th>ราคารวมก่อน VAT</th>
                        <th>&nbsp;</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($row = mysqli_fetch_assoc($result)): ?>
                        <tr>
                            <td><?php echo $row['order_no']; ?></td>
                            <td><?php echo $row['requisition_no']; ?></td>
                            <td><?php echo $row['document_revision']; ?></td>
                            <td><?php echo convert_date_to_display($row['order_date']); ?></td>
                            <td><?php echo $row['short_name']; ?></td>
                            <td><?php echo $row['fullname']; ?></td>
                            <td><?php echo number_format($row['after_discount'], 2); ?></td>
                            <td>
                                <a href="<?php echo $base_url . '/orders-edit.php?id=' . $row['id']; ?>" class="btn btn-warning">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                <a href="<?php echo $base_url . '/orders-delete.php?id=' . $row['id']; ?>" class="btn btn-danger btn-delete">
                                    <i class="bi bi-trash"></i>
                                </a>
                                <a href="<?php echo $base_url . '/orders-pdf.php?id=' . $row['id']; ?>" target="_blank" class="btn btn-success">
                                    <i class="bi bi-printer"></i>
                                </a>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer clearfix">
        <ul class="pagination pagination-sm m-0 float-end">
            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>&sort_by=<?php echo $sort_by; ?>&sort_order=<?php echo $sort_order; ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
            <?php endfor; ?>
        </ul>
    </div>
</div>
<?php
    $content = ob_get_clean();
    $js_script = '<script src="' . $base_url . '/assets/js/order.js"></script>';
    include 'template_master.php';
?>
