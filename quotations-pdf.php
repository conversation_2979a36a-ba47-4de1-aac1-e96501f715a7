<?php
require_once __DIR__ . '/vendor/autoload.php'; // Ensure mPDF is installed via Composer
include 'config.php';

$mpdf = new \Mpdf\Mpdf([
    'margin_top' => 5,
    'margin_bottom' => 5,
    'margin_left' => 5,
    'margin_right' => 5,
    'default_font_size' => 12,
]);

if (!isset($_GET['id'])) {
    die('Quotation ID is required.');
}
$quotation_id = intval($_GET['id']);

// Fetch quotation
$query = "
    SELECT q.*, 
           c.contact_name AS customer_contact_name, 
           c.fullname AS customer_fullname, 
           c.address AS customer_address, 
           c.tel AS customer_tel, 
           c.tax_no AS customer_tax_no,
           c.branch_name AS customer_branch_name,
           q.sub_total,
           q.discount,
           q.vat_amount,
           q.grand_total
    FROM quotations q 
    INNER JOIN customers c ON q.customer_id = c.id
    WHERE q.id = $quotation_id";
$result = mysqli_query($conn, $query);
$quotation =  mysqli_fetch_assoc($result);

// Fetch quotation items
$query_detail = "SELECT * FROM quotation_details WHERE quotation_id = $quotation_id";
$quotation_details = mysqli_query($conn, $query_detail);

// Count rows in quotation details
$row_count = mysqli_num_rows($quotation_details);

// Generate PDF content
$html = generateHTML($quotation);

$mpdf->AddPage();
$mpdf->WriteHTML($html);

$mpdf->SetXY(190, 5);
$mpdf->WriteCell(43, 5, "Page {PAGENO} of {nb}", 0, 0, 'R');

$y_reset = 130;
$y = $y_reset;
$row = 1;
foreach ($quotation_details as $detail) {
    // Set row number
    $mpdf->SetXY(9, $y);
    $mpdf->MultiCell(100, 5, strval($row), 0, 1, 'R');

    // Product code
    $mpdf->SetXY(18, $y);
    $mpdf->MultiCell(100, 5, $detail['product_code'], 0, 1, 'L');

    // Quantity
    $mpdf->SetXY(124, $y);
    $mpdf->MultiCell(20, 5, $detail['quantity'], 0, 1, 'L');

    // Unit name
    $mpdf->SetXY(138, $y);
    $mpdf->MultiCell(20, 5, $detail['unit_name'], 0, 1, 'L');

    // Price
    $mpdf->SetXY(150.5, $y);
    $mpdf->MultiCell(20, 5, number_format($detail['price'], 2), 0, 'R');
    
    // Discount
    $mpdf->SetXY(164, $y);
    $mpdf->MultiCell(20, 5, number_format($detail['discount'], 2), 0, 'R');

    // Total
    $mpdf->SetXY(182.5, $y);
    $mpdf->MultiCell(25, 5, number_format($detail['total'], 2), 0, 'R');

    // Product name
    $mpdf->SetXY(42, $y);
    $mpdf->MultiCell(100, 5, $detail['product_name'], 0, 1, 'L');
    $y = $mpdf->y;

    // Check if a new page is needed
    if ($y >= 204 && $row_count > $row) {
        $y = $y_reset;
        $mpdf->AddPage();
        $mpdf->WriteHTML($html);

        // Add page number
        $mpdf->SetXY(190, 5);
        $mpdf->WriteCell(43, 5, "Page {PAGENO} of {nb}", 0, 0, 'R');
    }

    $row++;
}





$mpdf->Output('quotation-' . $quotation['document_no'] . '.pdf', 'I');

function generateHTML($quotation) {
    ob_start();
    extract(['quotation' => $quotation]);
    include 'pdf/quotations.php';
    $html = ob_get_clean();
    return $html;
}
?>
