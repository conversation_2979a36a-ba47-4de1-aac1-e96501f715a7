<?php
    include 'config.php';
    include 'auth.php';
    $page_title = 'แก้ไขใบรับสินค้า';
    ob_start();

    // Fetch orders_reservation data
    $orders_reservation_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

    if ($orders_reservation_id <= 0) {
        $_SESSION['alert_text'] = 'Invalid orders_reservation ID';
        $_SESSION['alert_icon'] = 'error';
        header('Location: ' . $base_url . '/orders-reservation-list.php');
        exit();
    }

    $query = "
        SELECT 
            r.*, 
            s.short_name, 
            s.fullname AS supplier_name, 
            s.contact_name, 
            s.credit_day, 
            s.payment_type, 
            s.vat_type,
            s.withholding_type
        FROM 
            orders_reservations r 
        INNER JOIN 
            suppliers s 
        ON 
            r.supplier_id = s.id 
        WHERE 
            r.id = $orders_reservation_id
    ";
    $result = mysqli_query($conn, $query);
    $orders_reservation = mysqli_fetch_assoc($result);

    if (!$orders_reservation) {
        $_SESSION['alert_text'] = 'orders_reservation not found';
        $_SESSION['alert_icon'] = 'error';
        header('Location: ' . $base_url . '/orders-reservation-list.php');
        exit();
    }

    // Fetch order data by supplier_id
    $supplier_id = $orders_reservation['supplier_id'];
    $query_qo = "SELECT * FROM orders WHERE supplier_id = $supplier_id";
    $result_qo = mysqli_query($conn, $query_qo);

    // Fetch order details
    $query_details = "SELECT * FROM orders_reservation_details WHERE orders_reservation_id = $orders_reservation_id";
    $result_details = mysqli_query($conn, $query_details);
    $details = [];
    while ($row = mysqli_fetch_assoc($result_details)) {
        $details[] = $row;
    }

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // Assign fields to variables
        $document_date = convert_date_to_db($_POST['document_date']);
        $document_due_date = convert_date_to_db($_POST['document_due_date']);
        $quotation_no = $_POST['quotation_no'];
        $credit_day = $_POST['credit_day'];
        $payment_type = $_POST['payment_type'];
        $vat_type = $_POST['vat_type'];
        $withholding_type = $_POST['withholding_type'];
        $withholding_amount = $_POST['withholding_amount'];
        $sub_total = $_POST['sub_total'];
        $discount = $_POST['discount'];
        $after_discount = $_POST['after_discount'];
        $vat_amount = $_POST['vat_amount'];
        $grand_total = $_POST['grand_total'];
        $grand_total_with = $_POST['grand_total_with'];
        $note = $_POST['note'];
        $created_at = date('Y-m-d H:i:s');
        $updated_at = date('Y-m-d H:i:s');
        $user_id = $_SESSION['admin_id'];
        $supplier_id = $_POST['supplier_id'];
        $order_id = $_POST['order_id'] ?? null; // Set to null if not set
        $status_void = $_POST['status_void'];

        // Handle image upload
        $profile_image = isset($orders_reservation['profile_image']) ? $orders_reservation['profile_image'] : '';
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
            $target_dir = "upload_image/product/";
            $file_extension = pathinfo($_FILES["profile_image"]["name"], PATHINFO_EXTENSION);
            $unique_file_name = date('YmdHis') . rand(1000, 9999) . '.' . $file_extension;
            $target_file = $target_dir . $unique_file_name;
            if (move_uploaded_file($_FILES["profile_image"]["tmp_name"], $target_file)) {
                $profile_image = $unique_file_name;
            }
        }        // Add this before your INSERT query to check if the column exists and add it if needed
        $check_column_query = "SHOW COLUMNS FROM orders_reservations LIKE 'order_date'";
        $check_column_result = mysqli_query($conn, $check_column_query);

        if (mysqli_num_rows($check_column_result) == 0) {
            // If order_date column doesn't exist, add it
            $alter_table_query = "ALTER TABLE orders_reservations ADD COLUMN order_date DATE DEFAULT NULL";
            mysqli_query($conn, $alter_table_query);
        }

        // Then modify your INSERT query to match the table structure
        $document_number = $orders_reservation['document_number']; // Use existing document number from the fetched record
        $query = "UPDATE orders_reservations SET 
        document_number = '$document_number',
        document_date = '$document_date', 
        document_due_date = '$document_due_date',
        quotation_no = '$quotation_no',
        credit_day = '$credit_day', 
        payment_type = '$payment_type', 
        vat_type = '$vat_type',
        withholding_type = '$withholding_type',
        withholding_amount = '$withholding_amount',
        sub_total = '$sub_total',
        discount = '$discount',
        after_discount = '$after_discount', 
        vat_amount = '$vat_amount',
        grand_total = '$grand_total',
        grand_total_with = '$grand_total_with',
        note = '$note',
        created_at = '$created_at', 
        updated_at = '$updated_at', 
        profile_image = '$profile_image',
        user_id = '$user_id', 
        supplier_id = '$supplier_id',
        order_id = '$order_id',
        order_date = '$document_date',
        status_void = '$status_void'
        WHERE id = $orders_reservation_id";
        mysqli_query($conn, $query);
        
        // get details      
        $old_quantity_query = "SELECT product_id, quantity FROM orders_reservation_details WHERE orders_reservation_id = $orders_reservation_id";
        $old_quantity_result = mysqli_query($conn, $old_quantity_query);
        $old_quantities = [];
        while ($old_quantity_row = mysqli_fetch_assoc($old_quantity_result)) {
            $old_quantities[$old_quantity_row['product_id']] = $old_quantity_row['quantity'];
        }

        mysqli_query($conn, "DELETE FROM orders_reservation_details WHERE orders_reservation_id = $orders_reservation_id");

        // Insert updated details
        $detail_no = 1;
        foreach ($_POST['details'] as $detail) {
            $orders_reservation_id = $orders_reservation_id;
            $product_id = $detail['product_id'];
            $product_code = $detail['product_code'];
            $product_name = $detail['product_name'];
            $quantity = $detail['quantity'];
            $unit_name = $detail['unit_name'];
            $store_id = $detail['store_id'];
            $price = $detail['price'];
            $discount = $detail['discount'];
            $total = ($price * $quantity) - $discount;

            $detail_query = "INSERT INTO orders_reservation_details 
            (
                id,
                orders_reservation_id,
                product_id,
                product_code, 
                product_name, 
                quantity, 
                unit_name, 
                store_id,
                price, 
                discount, 
                total
            ) 
            VALUES 
            (
                '$detail_no',
                '$orders_reservation_id',
                '$product_id',
                '$product_code', 
                '$product_name', 
                '$quantity', 
                '$unit_name', 
                '$store_id',
                '$price', 
                '$discount', 
                '$total'
            )";
            mysqli_query($conn, $detail_query);

            $detail_no++; // detail_no+1


            // ถ้ามีการเลือกสถานะเป็น ยกเลิกเอกสาร (VOID) ให้คืนสินค้าในระบบ
            $old_quantity = isset($old_quantities[$product_id]) ? $old_quantities[$product_id] : 0;
            // Calculate the difference in quantity
            $quantity_difference = $quantity - $old_quantity;

            if ($status_void === 'YES') {
                // คืนสินค้า: เพิ่มจำนวนสินค้าในสต็อกกลับ
                $return_query = "UPDATE products_orders SET quantity = quantity + $old_quantity WHERE product_code = '$product_code'";
                mysqli_query($conn, $return_query);
            } else {
                // ปรับสต็อกตามปกติ
                $update_query = "UPDATE products_orders SET quantity = quantity - $quantity_difference, store_id = '$store_id' WHERE product_code = '$product_code'";
                mysqli_query($conn, $update_query);
            }

            // Update the orders to orders_reservation_id set the quantity to 0 (if order_id is set)  
            $update_orders_query = "UPDATE orders SET orders_reservation_id = NULL WHERE id = " . ($order_id ? "'$order_id'" : 'NULL');
            mysqli_query($conn, $update_orders_query);
        }
        $_SESSION['alert_text'] = 'ใบรับสินค้าถูกเพิ่มเรียบร้อยแล้ว';
        $_SESSION['alert_icon'] = 'success';
        
        header('Location: ' . $base_url . '/orders-reservation-list.php');
        exit();
    }
?>
<div id="appvue">    
    <form method="POST" enctype="multipart/form-data">
        <div class="card">
            <div class="card-header" style="background-color:rgb(191, 239, 248);">
                <h3 class="card-title">เพิ่มใบรับสินค้า</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="supplier_id">รหัสผู้ขาย</label>
                            <select name="supplier_id" id="supplier_id" class="form-select select2 " required>
                                <option value="0">เลือกข้อมูล</option>
                                <?php
                                    $result = mysqli_query($conn, "SELECT id, short_name FROM suppliers");
                                    while ($row = mysqli_fetch_assoc($result)):
                                ?>
                                    <option value="<?php echo $row['id']; ?>" <?php echo ($row['id'] == $orders_reservation['supplier_id']) ? 'selected' : ''; ?>>
                                        <?php echo $row['short_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="order_id">เลขที่ใบสั่งซื้อ</label>
                            <select name="order_id" id="order_id" class="form-select">
                                <option value="">-- เลือกข้อมูล --</option>
                                <?php foreach ($result_qo as $order): ?>
                                    <option value="<?php echo $order['id']; ?>" <?php echo ($order['id'] == $orders_reservation['order_id']) ? 'selected' : ''; ?>>
                                        <?php echo $order['order_no']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="document_number">เลขที่เอกสาร</label>
                            <input type="text" name="document_number" id="document_number" class="form-control" value="<?php echo $orders_reservation['document_number']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="document_date">วันที่</label>
                            <input type="text" value="<?php echo convert_date_to_display($orders_reservation['document_date']); ?>" name="document_date" id="document_date" class="form-control datepicker" required>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="document_due_date">วันกำหนดชำระเงิน</label>
                            <input type="text" value="<?php echo convert_date_to_display($orders_reservation['document_due_date']); ?>" name="document_due_date" id="document_due_date" class="form-control datepicker" required>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="short_name">ชื่อย่อบริษัท</label>
                            <input type="text" name="short_name" id="short_name" class="form-control bg-light" value="<?php echo $orders_reservation['short_name']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="supplier_name">ชื่อบริษัท</label>
                            <input type="text" name="supplier_name" id="supplier_name" class="form-control bg-light" value="<?php echo $orders_reservation['supplier_name']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="contact_name">ชื่อผู้ติดต่อ</label>
                            <input type="text" name="contact_name" id="contact_name" class="form-control bg-light" value="<?php echo $orders_reservation['contact_name']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="admin_name">พนักงานขาย</label>
                            <input type="text" name="admin_name" id="admin_name" value="<?php echo $_SESSION['admin_name']; ?>" class="form-control bg-light" readonly>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="department">แผนก</label>
                            <input type="text" name="department" id="department" value="<?php echo $_SESSION['departments_name']; ?>" class="form-control bg-light" readonly>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="quotation_no">เลขที่ใบเสนอราคา</label>
                            <input type="text" name="quotation_no" id="quotation_no" class="form-control" value="<?php echo $orders_reservation['quotation_no']; ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="payment_type">การชำระเงิน</label>
                            <input type="text" name="payment_type" id="payment_type" class="form-control bg-light" value="<?php echo $orders_reservation['payment_type']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="credit_day">เครดิต (วัน)</label>
                            <input type="number" name="credit_day" id="credit_day" class="form-control bg-light" value="<?php echo $orders_reservation['credit_day']; ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="vat_type_name">ชนิดภาษี</label>
                            <input type="text" name="vat_type_name" id="vat_type_name" value="<?php echo trans_vattype_to_display($orders_reservation['vat_type']); ?>" class="form-control bg-light" readonly>
                            <input type="hidden" name="vat_type" id="vat_type" value="<?php echo $orders_reservation['vat_type']; ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="withholding_type">หัก ณ ที่จ่าย</label>
                            <input type="text" name="withholding_type" id="withholding_type" 
                                   value="<?php echo ($orders_reservation['withholding_type']); ?>" 
                                   class="form-control bg-light" readonly>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="invoice_no">เลขที่ใบกำกับภาษี</label>                            
                            <input type="text" name="invoice_no" id="invoice_no" class="form-control" value="<?php echo $orders_reservation['invoice_no']; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="profile_image">PDF</label>
                            <input type="file" name="profile_image" id="profile_image" class="form-control" accept="application/pdf">
                            <?php if (!empty($orders_reservation['profile_image'])): ?>
                                <a href="<?php echo $base_url; ?>/upload_image/product/<?php echo $orders_reservation['profile_image']; ?>" target="_blank">
                                    ดาวน์โหลดไฟล์ PDF
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status_void" style="color: red;">สถานะเอกสาร</label>
                            <select name="status_void" id="status_void" class="form-select">
                                <option value="NO">ปกติ</option> 
                                <option value="YES" <?php echo ($orders_reservation['status_void'] == 'YES') ? 'selected' : ''; ?>>ยกเลิกเอกสาร</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="note">หมายเหตุ</label>
                            <textarea name="note" id="note" class="form-control" rows="3" ><?php echo $orders_reservation['note']; ?></textarea>
                        </div> 
                    </div>
                    <!-- table item list -->
                    <div class="row mt-5">
                    <div class="col-md-12 mb-3">
                        <button type="button" @click="openProductModal" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> เลือกสินค้าจากใบสั่งซื้อ
                        </button>
                    </div>
                    <div class="col md-12">
                        <div class="table-responsive">
                        <table class="table table-bordered border-primary">
                                <thead class="table-info">
                                    <tr>
                                        <th class="text-center">ลำดับ</th>
                                        <th>รหัสสินค้า</th>
                                        <th>ชื่อสินค้า</th>
                                        <th class="text-center">จำนวน</th>
                                        <th class="text-center">หน่วย</th>
                                        <th class="text-center">คลังค้า</th>
                                        <th class="text-end">ราคา</th>
                                        <th class="text-end">ส่วนลด</th>
                                        <th class="text-end">ค่า</th>
                                        <th class="text-center">PDF</th>
                                        <th>&nbsp;</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <template v-if="orderreservationItems.length > 0">
                                        <tr v-for="(item, index) in orderreservationItems" :key="index">
                                            <td class="text-center">{{ index+1 }}</td>                                            <td>
                                                {{ item.product_code }}
                                                <input type="hidden" :name="'details['+ index +'][product_id]'" :value="item.id">
                                                <input type="hidden" :name="'details['+ index +'][product_code]'" :value="item.product_code">
                                            </td>
                                            <td>
                                                {{ item.product_name }}
                                                <input type="hidden" :name="'details['+ index +'][product_name]'" :value="item.product_name">
                                            </td>
                                            <td class="text-end">
                                                <input type="number" v-model="item.quantity" :name="'details['+ index +'][quantity]'" class="form-control form-control-sm text-end" required>
                                            </td>
                                            <td class="text-center">
                                                {{ item.unit_name }}
                                                <input type="hidden" :name="'details['+ index +'][unit_name]'" :value="item.unit_name">
                                            </td>
                                            <td class="text-center">
                                                {{ item.store_name }}
                                                <select v-model="item.store_id" :name="'details['+ index +'][store_id]'" class="form-select form-select-sm" required>
                                                    <option value="">คลัง</option>
                                                    <?php
                                                        $result = mysqli_query($conn, "SELECT id, store_code, name FROM stores");
                                                        while ($row = mysqli_fetch_assoc($result)):
                                                    ?>
                                                        <option value="<?php echo $row['id']; ?>"><?php echo $row['store_code'] . ' - ' . $row['name']; ?></option>
                                                    <?php endwhile; ?>
                                                </select>
                                            </td>
                                            <td class="text-end">
                                                <input type="number" v-model="item.price" :name="'details['+ index +'][price]'" class="form-control form-control-sm text-end" required>
                                            </td>
                                            <td class="text-end">
                                                <input type="number" v-model="item.discount" :name="'details['+ index +'][discount]'" class="form-control form-control-sm text-end" required>
                                            </td>
                                            <td class="text-end">{{ calculateTotal(item).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</td>
                                            <td class="text-center">
                                                <a v-if="item.pdf && item.pdf.length > 0" :href="item.pdf" target="_blank" class="btn btn-info"><i class="bi bi-file-pdf"></i></a>
                                            </td>
                                            <td class="text-center">
                                                <button type="button" @click="removeOrderreservationItem(index)" class="btn btn-danger"><i class="bi bi-trash"></i></button>
                                            </td>
                                        </tr>
                                    </template>
                                    <template v-else>
                                        <tr>
                                            <td colspan="10" class="text-center">ไม่ข้อมูล</td>
                                        </tr>   
                                    </template>

                                    <tr>
                                        <td colspan="7" class="text-end"><strong>รวมจำนวน</strong></td>
                                        <td class="text-end">
                                            {{ calculateSubTotal().toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                            <input type="hidden" name="sub_total" :value="subTotal">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>       
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>ส่วนลดในใบ</strong></td>
                                        <td class="text-end">
                                            <input type="text" name="discount" id="discount" @keyup="calculateGrandTotal()" value="0" class="form-control form-control-sm text-end">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>รวมหักส่วนลด</strong></td>
                                        <td class="text-end">
                                            {{ Number(afterDiscount).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                            <input type="hidden" name="after_discount" :value="afterDiscount">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>ค่าภาษี 7%</strong></td>
                                        <td class="text-end">
                                            {{ Number(vat).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                            <input type="hidden" name="vat_amount" :value="vat">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>ราคาทั้งหมด</strong></td>
                                        <td class="text-end">
                                            {{ Number(calculateGrandTotal()).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                            <input type="hidden" name="grand_total" :value="grandTotal">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>ยอดหัก ณ ที่จ่าย</strong></td>
                                        <td class="text-end">
                                            {{ Number(withholding).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                            <input type="hidden" name="withholding_amount" :value="withholding">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="text-end"><strong>ราคารวมหัก ณ ที่จ่าย</strong></td>
                                        <td class="text-end">
                                            {{ Number(calculateGrandTotalMinusWithholding()).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
                                            <input type="hidden" name="grand_total_with" :value="calculateGrandTotalMinusWithholding()">
                                        </td>
                                        <td colspan="2"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <?php if($orders_reservation['status_void'] == 'YES'): ?>
                    <button type="submit" class="btn btn-danger me-1" disabled><i class="bi bi-x-circle me-1"></i>เอกสารถูกยกเลิก</button>
                <?php else: ?>
                    <button type="submit" class="btn btn-primary me-1"><i class="bi bi-floppy me-1"></i>บันทึกข้อมูล</button>    
                <?php endif; ?>                
                <a href="<?php echo $base_url . '/orders-reservation-list.php'; ?>" class='btn btn-secondary'>ย้อนกลับ</a>
            </div>
        </div>
    </form>
    <div class="modal fade" id="productModal" tabindex="-1" aria-labelledby="productModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="productModalLabel">เลือกสินค้าจากใบสั่งซื้อ</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>                <div class="modal-body">
                    <!-- Search Input -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" 
                                       v-model="searchQuery" 
                                       @keyup.enter="searchProducts"
                                       class="form-control" 
                                       placeholder="ค้นหาสินค้า (รหัสสินค้า หรือ ชื่อสินค้า)">
                            </div>
                            <div class="col-md-2">
                                <button type="button" @click="searchProducts" class="btn btn-outline-secondary">
                                    <i class="bi bi-search"></i> ค้นหา
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">                        
                        <table class="table table-bordered border-primary">                              
                            <thead class="table-info">
                                <tr>  
                                    <th>
                                        <input type="checkbox" v-model="selectAll" @change="toggleSelectAll">
                                    </th>     
                                    <th>รหัสสินค้า</th>
                                    <th>ชื่อสินค้า</th>
                                    <th>จำนวน</th>
                                    <th>หน่วย</th>
                                    <th>คลังค้า</th>
                                    <th>ราคา</th>
                                </tr>
                            </thead>
                            <tbody v-if="productsorders.length === 0">
                                <tr>
                                    <td colspan="7" class="text-center">ไม่มีข้อมูล</td>
                                </tr>
                            </tbody>                            
                            <tbody v-else> 
                                <tr v-for="(productorder, index) in productsorders" :key="index">
                                    <td><input type="checkbox" v-model="productorder.selected" @change="updateSelectAll"></td>
                                    <td>{{ productorder.product_code }}</td>
                                    <td>{{ productorder.product_name }}</td>
                                    <td>{{ productorder.quantity }}</td>
                                    <td>{{ productorder.unit_name }}</td>
                                    <td>{{ productorder.store_name || '-' }}</td>
                                    <td>{{ productorder.price }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination Controls -->
                    <div class="d-flex justify-content-between align-items-center mt-3" v-if="totalPages > 1">
                        <div>
                            <span class="text-muted">
                                แสดง {{ (currentPage - 1) * itemsPerPage + 1 }} - {{ Math.min(currentPage * itemsPerPage, totalProducts) }} 
                                จาก {{ totalProducts }} รายการ
                            </span>
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                    <button class="page-link" @click="prevPage" :disabled="currentPage === 1">
                                        <i class="bi bi-chevron-left"></i>
                                    </button>
                                </li>
                                
                                <li v-for="page in Math.min(totalPages, 5)" :key="page" 
                                    class="page-item" 
                                    :class="{ active: currentPage === page }">
                                    <button class="page-link" @click="goToPage(page)">
                                        {{ page }}
                                    </button>
                                </li>
                                
                                <li v-if="totalPages > 5 && currentPage < totalPages - 2" class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                
                                <li v-if="totalPages > 5 && currentPage < totalPages - 1" 
                                    class="page-item" 
                                    :class="{ active: currentPage === totalPages }">
                                    <button class="page-link" @click="goToPage(totalPages)">
                                        {{ totalPages }}
                                    </button>
                                </li>
                                
                                <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                    <button class="page-link" @click="nextPage" :disabled="currentPage === totalPages">
                                        <i class="bi bi-chevron-right"></i>
                                    </button>
                                </li>
                            </ul>
                        </nav>
                    </div>                
                    <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
                    <button type="button" @click="onSelectProductorder()" class="btn btn-primary">เลือกข้อมูล</button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
    $content = ob_get_clean();
    $js_script = '<script src="' . $base_url . '/assets/js/orders-reservation.js"></script>';
    include 'template_master.php';
?>
