<?php
    include 'config.php';
    include 'auth.php';
    $page_title = 'รายการสินค้าซื้อ';
    ob_start();

    // Handle search and filter
    $search_keyword = isset($_GET['search_keyword']) ? $_GET['search_keyword'] : '';
    $category_id = isset($_GET['category_id']) ? $_GET['category_id'] : '';

    // Handle sorting
    $sort_by = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'id';
    $sort_order = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'desc';
    $next_sort_order = $sort_order == 'asc' ? 'desc' : 'asc';

    // Pagination settings
    $limit = 10;
    $page = isset($_GET['page']) ? $_GET['page'] : 1;
    $offset = ($page - 1) * $limit;

    // Build query
    $query = "SELECT 
        p.id, 
        p.product_code, 
        p.product_name,
        p.quantity,
        p.price,
        p.unit_name,
        p.profile_image,
        m.short_name,
        mt.materials_name,
        COALESCE(SUM(CASE WHEN ord.status_void != 'YES' OR ord.status_void IS NULL THEN ord_details.quantity ELSE 0 END), 0) as orders_reservation_quantity,
        (p.quantity - COALESCE(SUM(CASE WHEN ord.status_void != 'YES' OR ord.status_void IS NULL THEN ord_details.quantity ELSE 0 END), 0)) as remaining
    FROM 
        products_orders p 
    INNER JOIN 
        suppliers m 
        ON p.supplier_id=m.id 
    INNER JOIN 
        materials mt 
        ON p.material_id=mt.id 
    LEFT JOIN orders_reservation_details ord_details
        ON p.id=ord_details.product_id
    LEFT JOIN orders_reservations ord
        ON ord_details.orders_reservation_id=ord.id
    WHERE 1=1";
    
    if ($search_keyword) {
        $query .= " AND (
        p.product_code LIKE '%$search_keyword%'
        OR p.product_name LIKE '%$search_keyword%' 
        OR m.short_name LIKE '%$search_keyword%'
        OR mt.materials_name LIKE '%$search_keyword%'
        )";
    }
 
    $query .= " GROUP BY p.id";     // Add GROUP BY clause to sum quantities by product_id
    $query .= " ORDER BY $sort_by $sort_order";
    $query .= " LIMIT $limit OFFSET $offset";
    $result = mysqli_query($conn, $query);

    // Get total records for pagination
    $total_query = "SELECT COUNT(*) as total 
    FROM 
        products_orders p 
    INNER JOIN 
        suppliers m 
        ON p.supplier_id=m.id 
    INNER JOIN 
        materials mt 
        ON p.material_id=mt.id 
    WHERE 1=1";
    if ($search_keyword) {
        $total_query .= " AND (
        p.product_code LIKE '%$search_keyword%'
        OR
        p.product_name LIKE '%$search_keyword%' 
        OR m.short_name LIKE '%$search_keyword%'
        OR mt.materials_name LIKE '%$search_keyword%'
        )";
    }

    $total_result = mysqli_query($conn, $total_query);
    $total_row = mysqli_fetch_assoc($total_result);
    $total_records = $total_row['total'];
    $total_pages = ceil($total_records / $limit);

    // Determine sort icon
    function get_sort_icon($current_sort_by, $current_sort_order, $column) {
        if ($current_sort_by == $column) {
            return $current_sort_order == 'asc' ? '<i class="bi bi-sort-alpha-down"></i>' : '<i class="bi bi-sort-alpha-down-alt"></i>';
        }
        return '';
    }
?>
<div class="card">
    <div class="card-header">
        <h3 class="card-title">รายการสินค้าซื้อ</h3>
        <div class="card-tools">
            <a href="<?php echo $base_url; ?>/products-orders-add.php" class="btn btn-primary">เพิ่มสินค้า</a>
        </div>
    </div>
    <div class="card-body">
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger">
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            </div>
        <?php endif; ?>
        <form method="GET" class="mb-3">
            <div class="row">
                <div class="col-md-4">
                    <input type="text" name="search_keyword" class="form-control" placeholder="ค้นหาสินค้า" value="<?php echo $search_keyword; ?>">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary">ค้นหา</button>
                </div>
            </div>
        </form>
        <table class="table table-bordered table-hover">
            <thead class="thead-dark">
            <tr>
                <th>
                <a href="?sort_by=product_code&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                    รหัสสินค้า <?php echo get_sort_icon($sort_by, $sort_order, 'product_code'); ?>
                </a>
                </th>
                <th>ซัพพลายเออร์</th>
                <th>
                <a href="?sort_by=product_name&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                    สินค้า <?php echo get_sort_icon($sort_by, $sort_order, 'product_name'); ?>
                </a>
                </th>
                <th>
                    <a href="?sort_by=materials_name&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                        วัตถุดิบ <?php echo get_sort_icon($sort_by, $sort_order, 'materials_name'); ?>
                    </a>
                </th>
                <th>
                    <a href="?sort_by=quantity&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                        ยอดค้างรับ <?php echo get_sort_icon($sort_by, $sort_order, 'quantity'); ?>
                    </a>
                </th>
                <th>
                    <a href="?sort_by=orders_reservation_details_quantity&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                        ยอดรับ <?php echo get_sort_icon($sort_by, $sort_order, 'orders_reservation_details_quantity'); ?>
                    </a>
                </th>
                <th>
                    <a href="?sort_by=remaining&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                        ยอดคงเหลือ <?php echo get_sort_icon($sort_by, $sort_order, 'remaining'); ?>
                    </a>
                </th>
                <th>
                    <a href="?sort_by=price&sort_order=<?php echo $next_sort_order; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>">
                        ราคา <?php echo get_sort_icon($sort_by, $sort_order, 'price'); ?>
                    </a>
                </th>
                <th>
                    หน่วยนับ
                </th>
                <th>
                    การจัดการ
                </th>
            </tr>
            </thead>
            <tbody>
                <?php while ($row = mysqli_fetch_assoc($result)): ?>
                    <tr>
                        <!-- <td><?php echo @$l+=1; ?></td> -->
                        <td><?php echo $row['product_code']; ?></td>
                        <td><?php echo $row['short_name']; ?></td>
                        <td><?php echo $row['product_name']; ?></td>
                        <td><?php echo $row['materials_name']; ?></td>
                        <td><?php echo $row['quantity']; ?></td>
                        <td><?php echo $row['orders_reservation_quantity']; ?></td>
                        <td><?php echo $row['remaining'] > 0 ? $row['remaining'] : 0;  ?></td>
                        <td><?php echo number_format($row['price'], 2); ?></td>
                        <td><?php echo $row['unit_name']; ?></td>
                        <td>
                            <!-- //image -->
                            <?php if(!empty($row['profile_image'])): ?>
                            <a href="<?php echo $base_url; ?>/upload_image/product/<?php echo $row['profile_image']; ?>" target="_blank" class="btn btn-info btn">
                                <i class="bi bi-file-earmark-pdf me-1"></i>
                            </a>
                            <?php endif; ?>
                            <!-- //แก้ไข -->
                            <a href="<?php echo $base_url . '/products-orders-edit.php?id=' . $row['id']; ?>" class='btn btn-warning'>
                                <i class="bi bi-pencil-square me-1"></i>
                            </a>
                            <!-- //ลบ -->
                            <button class='btn btn-danger' onclick="confirmDelete(<?php echo $row['id']; ?>)">
                                <i class="bi bi-trash me-1"></i>
                            </button>
                            <script>
                                function confirmDelete(productId) {
                                    Swal.fire({
                                        title: 'ยืนยันการลบสินค้านี้?',
                                        text: "คุณจะไม่สามารถกู้คืนข้อมูลนี้ได้!",
                                        icon: 'warning',
                                        showCancelButton: true,
                                        confirmButtonColor: '#d33',
                                        cancelButtonColor: '#3085d6',
                                        confirmButtonText: 'ใช่, ลบเลย!',
                                        cancelButtonText: 'ยกเลิก'
                                    }).then((result) => {
                                        if (result.isConfirmed) {
                                            window.location.href = '<?php echo $base_url; ?>/products-orders-delete.php?id=' + productId;
                                        }
                                    });
                                }
                            </script>
                            </a>
                        </td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
    <div class="card-footer clearfix">
        <ul class="pagination pagination-sm m-0 float-end">
            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&search_keyword=<?php echo $search_keyword; ?>&category_id=<?php echo $category_id; ?>&sort_by=<?php echo $sort_by; ?>&sort_order=<?php echo $sort_order; ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
            <?php endfor; ?>
        </ul>
    </div>
</div>
<?php
    $content = ob_get_clean();
    $js_script = '<script src="assets/js/product.js"></script>';
    include 'template_master.php';
?>
